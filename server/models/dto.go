package models

import "time"

// --- Request DTOs ---

type SearchRequest struct {
	Keyword string `form:"keyword" binding:"required"`
	Type    string `form:"type" binding:"omitempty,oneof=all actor drama cut news"`
	Page    int    `form:"page" binding:"omitempty,min=1"`
	Limit   int    `form:"limit" binding:"omitempty,min=1,max=50"`
}

type CommentCreateRequest struct {
	TargetType string `json:"targetType" binding:"required,oneof=drama actor"`
	TargetID   string `json:"targetId" binding:"required"`
	Content    string `json:"content" binding:"required,max=500"`
}

type PaymentUnlockRequest struct {
	ContentType   string `json:"contentType" binding:"required,oneof=drama cut"`
	ContentID     string `json:"contentId" binding:"required"`
	PaymentMethod string `json:"paymentMethod" binding:"omitempty,oneof=wechat alipay apple_pay"`
}

type PaginationRequest struct {
	Page  int `form:"page" binding:"omitempty,min=1"`
	Limit int `form:"limit" binding:"omitempty,min=1,max=50"`
}

// --- Attachment DTOs ---

// AttachmentOptions nocobase字段配置中的附件选项
type AttachmentOptions struct {
	Target   string `json:"target"`
	Storage  string `json:"storage"`
	Through  string `json:"through"`
	OtherKey string `json:"otherKey"`
	UiSchema struct {
		Type            string `json:"type"`
		Title           string `json:"title"`
		XComponent      string `json:"x-component"`
		XComponentProps struct {
			Accept   string `json:"accept"`
			Multiple bool   `json:"multiple"`
		} `json:"x-component-props"`
		XUseComponentProps string `json:"x-use-component-props"`
	} `json:"uiSchema"`
	SourceKey  string `json:"sourceKey"`
	TargetKey  string `json:"targetKey"`
	ForeignKey string `json:"foreignKey"`
}

// From 用于指定要获取附件的来源信息
type From struct {
	Collection string `json:"collection"`
	FieldName  string `json:"fieldName"`
	ID         int64  `json:"id"`
}

// --- Response DTOs ---

type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

type PaginationResponse struct {
	Page       int  `json:"page"`
	Limit      int  `json:"limit"`
	Total      int  `json:"total"`
	TotalPages int  `json:"totalPages"`
	HasNext    bool `json:"hasNext"`
	HasPrev    bool `json:"hasPrev"`
}

type HomeRecommendationData struct {
	TodayFortune TodayFortuneData  `json:"todayFortune"`
	Dramas       []*DuanjuResponse `json:"dramas"`
}

type RecommendationRowResponse struct {
	Actor DuanjuActorResponse `json:"actor"`
}

type TodayFortuneData struct {
	Date string `json:"date"`
	Good string `json:"good"`
	Bad  string `json:"bad"`
}

type SearchResponseData struct {
	Actors     []ActorResponse    `json:"actors,omitempty"`
	Dramas     []DramaResponse    `json:"dramas,omitempty"`
	Cuts       []CutResponse      `json:"cuts,omitempty"`
	News       []NewsResponse     `json:"news,omitempty"`
	Pagination PaginationResponse `json:"pagination"`
}

type DramaResponse struct {
	ID             string          `json:"id"`
	Title          string          `json:"title"`
	Cover          *string         `json:"cover"`
	Producer       *string         `json:"producer"`
	Episodes       int             `json:"episodes"`
	Heat           *string         `json:"heat"`
	PlayCount      *string         `json:"playCount"`
	Description    *string         `json:"description,omitempty"`
	Recommendation *string         `json:"recommendation,omitempty"`
	Actors         []ActorResponse `json:"actors,omitempty"`
	Tags           []string        `json:"tags,omitempty"`
}

type ActorResponse struct {
	ID             string            `json:"id"`
	Name           string            `json:"name"`
	Avatar         *string           `json:"avatar"`
	Gender         string            `json:"gender"`
	Heat           *string           `json:"heat"`
	Representative *string           `json:"representative"`
	IsFollowed     bool              `json:"isFollowed"`
	Bio            *string           `json:"bio,omitempty"`
	SocialLinks    *ActorSocialLinks `json:"socialLinks,omitempty"`
}

type ActorSocialLinks struct {
	Weibo       *string `json:"weibo,omitempty"`
	Tiktok      *string `json:"tiktok,omitempty"`
	Xiaohongshu *string `json:"xiaohongshu,omitempty"`
	Weixin      *string `json:"weixin,omitempty"`
}

type CutResponse struct {
	ID        string  `json:"id"`
	Title     string  `json:"title"`
	Cover     *string `json:"cover"`
	Duration  *string `json:"duration"`
	PlayCount *string `json:"playCount"`
	NeedPay   bool    `json:"needPay"`
	VideoURL  *string `json:"videoUrl,omitempty"`
}

type NewsResponse struct {
	ID          string  `json:"id"`
	Title       string  `json:"title"`
	Description *string `json:"description"`
	Source      *string `json:"source"`
	PublishTime *string `json:"publishTime"`
	Cover       *string `json:"cover"`
}

type CommentResponse struct {
	ID        string    `json:"id"`
	Author    string    `json:"author"`
	Avatar    *string   `json:"avatar"`
	Content   string    `json:"content"`
	Time      string    `json:"time"`
	CreatedAt time.Time `json:"createdAt"`
}

type TagResponse struct {
	ID          string  `json:"id"`
	Name        string  `json:"name"`
	Heat        *string `json:"heat"`
	Description *string `json:"description,omitempty"`
}

type TagSectionResponse struct {
	Tag    TagResponse     `json:"tag"`
	Dramas []DramaResponse `json:"dramas"`
}

type PaymentStatusData struct {
	IsPaid     bool       `json:"isPaid"`
	UnlockedAt *time.Time `json:"unlockedAt,omitempty"`
	OrderID    *string    `json:"orderId,omitempty"`
}

type PaymentResponseData struct {
	OrderID    string     `json:"orderId"`
	Unlocked   bool       `json:"unlocked"`
	UnlockedAt *time.Time `json:"unlockedAt,omitempty"`
}

// --- List Response Wrappers ---

type DramaListResponse struct {
	Dramas     []DramaResponse    `json:"dramas"`
	Pagination PaginationResponse `json:"pagination"`
}

type ActorListResponse struct {
	Actors     []ActorResponse    `json:"actors"`
	Pagination PaginationResponse `json:"pagination"`
}

type CutListResponse struct {
	Cuts           []CutResponse      `json:"cuts"`
	HasPaidContent bool               `json:"hasPaidContent"`
	Pagination     PaginationResponse `json:"pagination"`
}

type NewsListResponse struct {
	News       []NewsResponse     `json:"news"`
	Pagination PaginationResponse `json:"pagination"`
}

type CommentListResponse struct {
	Comments   []CommentResponse  `json:"comments"`
	Total      int                `json:"total"`
	Pagination PaginationResponse `json:"pagination"`
}

type TagListResponse struct {
	Tags []TagResponse `json:"tags"`
}

type TagRankingResponse struct {
	Sections   []TagSectionResponse `json:"sections"`
	Pagination PaginationResponse   `json:"pagination"`
}

type ActorRankingResponse struct {
	Actors     []ActorResponse    `json:"actors"`
	Pagination PaginationResponse `json:"pagination"`
}

// --- 新的响应结构 (适配新的数据模型) ---

type DuanjuResponse struct {
	ID             uint64                   `json:"id"`
	BookName       *string                  `json:"bookName"`
	BookID         *string                  `json:"bookId"`
	SerialCount    *float64                 `json:"serialCount"`
	ReadCount      *uint64                  `json:"readCount"`
	Abstract       *string                  `json:"abstract,omitempty"`
	Platform       *string                  `json:"platform"`
	Recommendation *string                  `json:"recommendation,omitempty"`
	HeatCount      *uint64                  `json:"heatCount"`
	Actors         []DuanjuActorResponse    `json:"actors,omitempty"`
	Categories     []DuanjuCategoryResponse `json:"categories,omitempty"`
	Authors        []DuanjuAuthorResponse   `json:"authors,omitempty"`
}

type DuanjuActorResponse struct {
	ID          uint64                  `json:"id"`
	Name        *string                 `json:"name"`
	AvatarURL   *string                 `json:"avatarUrl"`
	Gender      *string                 `json:"gender"`
	Platform    *string                 `json:"platform"`
	ActInCounts *uint64                 `json:"actInCounts"`
	Desc        *string                 `json:"desc,omitempty"`
	Profile     *string                 `json:"profile,omitempty"`
	IsFollowed  bool                    `json:"isFollowed"`
	SocialLinks *DuanjuActorSocialLinks `json:"socialLinks,omitempty"`
}

type DuanjuActorSocialLinks struct {
	XhsID    *string `json:"xhsId,omitempty"`
	WeiboID  *string `json:"weiboId,omitempty"`
	DouyinID *string `json:"douyinId,omitempty"`
}

type DuanjuCategoryResponse struct {
	ID        uint64  `json:"id"`
	Name      *string `json:"name"`
	Platform  *string `json:"platform"`
	HeatCount *uint64 `json:"heatCount"`
	Desc      *string `json:"desc,omitempty"`
}

type DuanjuAuthorResponse struct {
	ID       uint64  `json:"id"`
	Name     *string `json:"name"`
	Platform *string `json:"platform"`
	Desc     *string `json:"desc,omitempty"`
}

type DuanjuActorCutResponse struct {
	ID        uint64  `json:"id"`
	Title     *string `json:"title"`
	Desc      *string `json:"desc,omitempty"`
	PlayCount *uint64 `json:"playCount"`
	Duration  *uint64 `json:"duration"`
}

type DuanjuSeriesCutResponse struct {
	ID        uint64  `json:"id"`
	Title     *string `json:"title"`
	Desc      *string `json:"desc,omitempty"`
	Duration  *uint64 `json:"duration"`
	PlayCount *uint64 `json:"playCount"`
}

type DuanjuListResponse struct {
	Duanjus    []DuanjuResponse   `json:"duanjus"`
	Pagination PaginationResponse `json:"pagination"`
}

type DuanjuActorListResponse struct {
	Actors     []DuanjuActorResponse `json:"actors"`
	Pagination PaginationResponse    `json:"pagination"`
}

type CategorySectionResponse struct {
	Category DuanjuCategoryResponse `json:"category"`
	Duanjus  []DuanjuResponse       `json:"duanjus"`
}

type CategoryRankingResponse struct {
	Sections   []CategorySectionResponse `json:"sections"`
	Pagination PaginationResponse        `json:"pagination"`
}

type DuanjuSeriesCutListResponse struct {
	Cuts       []DuanjuSeriesCutResponse `json:"cuts"`
	Pagination PaginationResponse        `json:"pagination"`
}

type DuanjuActorCutListResponse struct {
	Cuts       []DuanjuActorCutResponse `json:"cuts"`
	Pagination PaginationResponse       `json:"pagination"`
}

// DuanjuActorDetailResponse 演员详情响应结构（包含参演短剧、精彩片段和评论）
type DuanjuActorDetailResponse struct {
	Actor    DuanjuActorResponse      `json:"actor"`
	Dramas   []DuanjuResponse         `json:"dramas"`
	Cuts     []DuanjuActorCutResponse `json:"cuts"`
	Comments []CommentResponse        `json:"comments"`
}
