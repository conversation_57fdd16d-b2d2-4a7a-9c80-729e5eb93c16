package services

import (
	"strconv"

	"github.com/jony4/52kanduanju.mp/server/config"
	"github.com/jony4/52kanduanju.mp/server/dao"
	"github.com/jony4/52kanduanju.mp/server/models"
	"gorm.io/gorm"
)

// DuanjuActorService 短剧演员服务 (新结构)
type DuanjuActorService struct {
	*BaseService
	actorDAO              *dao.DuanjuActorDAO
	duanjuActorCommentDAO *dao.DuanjuActorCommentDAO
	paymentDAO            *dao.PaymentDAO
}

func NewDuanjuActorService(db *gorm.DB, cfg *config.Config) *DuanjuActorService {
	return &DuanjuActorService{
		BaseService:           NewBaseService(db, cfg),
		actorDAO:              dao.NewDuanjuActorDAO(db),
		duanjuActorCommentDAO: dao.NewDuanjuActorCommentDAO(db),
		paymentDAO:            dao.NewPaymentDAO(db),
	}
}

// GetActorDetail 获取演员详情（包含参演短剧、精彩片段和评论）
func (s *DuanjuActorService) GetActorDetail(actorIDStr string, userID uint64) (*models.DuanjuActorDetailResponse, error) {
	actorID, err := strconv.ParseUint(actorIDStr, 10, 64)
	if err != nil {
		return nil, err
	}

	actor, err := s.actorDAO.GetByID(actorID)
	if err != nil {
		return nil, err
	}

	// 检查用户是否关注了该演员
	isFollowed := false
	if userID > 0 {
		isFollowed, _ = s.actorDAO.IsFollowedByUser(userID, actorID)
	}

	// 获取演员参演的短剧（限制数量，避免数据过多）
	pagination := s.ValidatePagination(1, 10)
	dramas, _, err := s.actorDAO.GetDuanjus(actorID, pagination)
	if err != nil {
		return nil, err
	}

	// 获取演员的精彩片段
	cuts, err := s.actorDAO.GetActorCuts(actorID)
	if err != nil {
		return nil, err
	}

	// 获取演员的评论（限制数量）
	commentPagination := s.ValidatePagination(1, 10)
	comments, _, err := s.duanjuActorCommentDAO.GetList(actorID, commentPagination)
	if err != nil {
		return nil, err
	}

	// 转换响应数据
	actorResponse := s.ConvertDuanjuActorToResponse(actor)
	actorResponse.IsFollowed = isFollowed

	var dramaResponses []models.DuanjuResponse
	for _, drama := range dramas {
		dramaResponses = append(dramaResponses, *s.ConvertDuanjuToResponse(&drama))
	}

	var cutResponses []models.DuanjuActorCutResponse
	for _, cut := range cuts {
		cutResponses = append(cutResponses, *s.ConvertDuanjuActorCutToResponse(&cut))
	}

	var commentResponses []models.CommentResponse
	for _, comment := range comments {
		commentResponses = append(commentResponses, *s.ConvertDuanjuActorCommentToResponse(&comment))
	}

	return &models.DuanjuActorDetailResponse{
		Actor:    *actorResponse,
		Dramas:   dramaResponses,
		Cuts:     cutResponses,
		Comments: commentResponses,
	}, nil
}

// GetActorDuanjus 获取演员作品
func (s *DuanjuActorService) GetActorDuanjus(actorIDStr string, page, limit int) (*models.DuanjuListResponse, error) {
	actorID, err := strconv.ParseUint(actorIDStr, 10, 64)
	if err != nil {
		return nil, err
	}

	pagination := s.ValidatePagination(page, limit)

	duanjus, paginationResult, err := s.actorDAO.GetDuanjus(actorID, pagination)
	if err != nil {
		return nil, err
	}

	var duanjuResponses []models.DuanjuResponse
	for _, duanju := range duanjus {
		duanjuResponses = append(duanjuResponses, *s.ConvertDuanjuToResponse(&duanju))
	}

	return &models.DuanjuListResponse{
		Duanjus:    duanjuResponses,
		Pagination: s.ConvertPaginationResult(paginationResult),
	}, nil
}

// GetActorCuts 获取演员精彩片段
func (s *DuanjuActorService) GetActorCuts(actorIDStr string, userID uint64) (*models.DuanjuActorCutListResponse, error) {
	actorID, err := strconv.ParseUint(actorIDStr, 10, 64)
	if err != nil {
		return nil, err
	}

	// 获取演员的精彩片段
	cuts, err := s.actorDAO.GetActorCuts(actorID)
	if err != nil {
		return nil, err
	}

	var cutResponses []models.DuanjuActorCutResponse
	for _, cut := range cuts {
		cutResponses = append(cutResponses, *s.ConvertDuanjuActorCutToResponse(&cut))
	}

	return &models.DuanjuActorCutListResponse{
		Cuts: cutResponses,
		Pagination: models.PaginationResponse{
			Page:       1,
			Limit:      len(cutResponses),
			Total:      len(cutResponses),
			TotalPages: 1,
			HasNext:    false,
			HasPrev:    false,
		},
	}, nil
}

// FollowActor 关注演员
func (s *DuanjuActorService) FollowActor(userID uint64, actorIDStr string) error {
	actorID, err := strconv.ParseUint(actorIDStr, 10, 64)
	if err != nil {
		return err
	}

	// 检查演员是否存在
	_, err = s.actorDAO.GetByID(actorID)
	if err != nil {
		return err
	}

	// 检查是否已经关注
	isFollowed, err := s.actorDAO.IsFollowedByUser(userID, actorID)
	if err != nil {
		return err
	}
	if isFollowed {
		return nil // 已经关注，直接返回成功
	}

	return s.actorDAO.Follow(userID, actorID)
}

// UnfollowActor 取消关注演员
func (s *DuanjuActorService) UnfollowActor(userID uint64, actorIDStr string) error {
	actorID, err := strconv.ParseUint(actorIDStr, 10, 64)
	if err != nil {
		return err
	}

	return s.actorDAO.Unfollow(userID, actorID)
}

// ToggleFollowActor 切换关注状态，返回新的关注状态
func (s *DuanjuActorService) ToggleFollowActor(userID uint64, actorIDStr string) (bool, error) {
	actorID, err := strconv.ParseUint(actorIDStr, 10, 64)
	if err != nil {
		return false, err
	}

	// 检查演员是否存在
	_, err = s.actorDAO.GetByID(actorID)
	if err != nil {
		return false, err
	}

	// 检查当前关注状态
	isFollowed, err := s.actorDAO.IsFollowedByUser(userID, actorID)
	if err != nil {
		return false, err
	}

	if isFollowed {
		// 当前已关注，执行取消关注
		err = s.actorDAO.Unfollow(userID, actorID)
		if err != nil {
			return false, err
		}
		return false, nil
	} else {
		// 当前未关注，执行关注
		err = s.actorDAO.Follow(userID, actorID)
		if err != nil {
			return false, err
		}
		return true, nil
	}
}

// GetFollowedActors 获取用户关注的演员列表
func (s *DuanjuActorService) GetFollowedActors(userID uint64, page, limit int) (*models.DuanjuActorListResponse, error) {
	pagination := s.ValidatePagination(page, limit)

	actors, paginationResult, err := s.actorDAO.GetFollowedActors(userID, pagination)
	if err != nil {
		return nil, err
	}

	var actorResponses []models.DuanjuActorResponse
	for _, actor := range actors {
		response := s.ConvertDuanjuActorToResponse(&actor)
		response.IsFollowed = true // 关注列表中的演员都是已关注的
		actorResponses = append(actorResponses, *response)
	}

	return &models.DuanjuActorListResponse{
		Actors:     actorResponses,
		Pagination: s.ConvertPaginationResult(paginationResult),
	}, nil
}

// GetActorRanking 获取演员排行榜
func (s *DuanjuActorService) GetActorRanking(rankType string, page, limit int) (*models.DuanjuActorListResponse, error) {
	pagination := s.ValidatePagination(page, limit)

	actors, paginationResult, err := s.actorDAO.GetRanking(rankType, pagination)
	if err != nil {
		return nil, err
	}

	var actorResponses []models.DuanjuActorResponse
	for _, actor := range actors {
		response := s.ConvertDuanjuActorToResponse(&actor)
		// 排行榜中的演员关注状态需要单独查询，这里先设为false
		response.IsFollowed = false
		actorResponses = append(actorResponses, *response)
	}

	return &models.DuanjuActorListResponse{
		Actors:     actorResponses,
		Pagination: s.ConvertPaginationResult(paginationResult),
	}, nil
}
