{"openapi": "3.0.3", "info": {"title": "我爱看短剧 API", "description": "短剧平台后端API接口文档，支持短剧浏览、演员信息、用户交互、搜索、评论、付费等功能", "version": "1.0.0", "contact": {"name": "API Support", "email": "<EMAIL>"}}, "servers": [{"url": "https://api.52kanduanju.com/v1", "description": "生产环境"}, {"url": "https://dev-api.52kanduanju.com/v1", "description": "开发环境"}], "tags": [{"name": "drama", "description": "短剧管理"}, {"name": "actor", "description": "演员管理"}, {"name": "user", "description": "用户管理"}, {"name": "search", "description": "搜索功能"}, {"name": "comment", "description": "评论系统"}, {"name": "ranking", "description": "排行榜"}, {"name": "tag", "description": "标签系统"}, {"name": "payment", "description": "付费系统"}, {"name": "recommendation", "description": "内容推荐"}, {"name": "media", "description": "媒体内容"}], "paths": {"/recommendation/home": {"get": {"tags": ["recommendation"], "summary": "获取首页推荐内容", "description": "获取首页的今日运势、热门短剧和演员推荐", "responses": {"200": {"description": "成功获取推荐内容", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HomeRecommendationResponse"}}}}}}}, "/search": {"get": {"tags": ["search"], "summary": "综合搜索", "description": "根据关键词搜索演员、短剧、精彩片段、新闻", "parameters": [{"name": "keyword", "in": "query", "required": true, "description": "搜索关键词", "schema": {"type": "string", "example": "霸道总裁"}}, {"name": "type", "in": "query", "required": false, "description": "搜索类型", "schema": {"type": "string", "enum": ["all", "actor", "drama", "cut", "news"], "default": "all"}}, {"name": "page", "in": "query", "required": false, "description": "页码", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "schema": {"type": "integer", "minimum": 1, "maximum": 50, "default": 20}}], "responses": {"200": {"description": "搜索成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchResponse"}}}}}}}, "/dramas/{dramaId}": {"get": {"tags": ["drama"], "summary": "获取短剧详情", "description": "获取指定短剧的详细信息", "parameters": [{"name": "dramaId", "in": "path", "required": true, "description": "短剧ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取短剧详情", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DramaDetail"}}}}, "404": {"description": "短剧不存在"}}}}, "/dramas": {"get": {"tags": ["drama"], "summary": "获取短剧列表", "description": "根据条件获取短剧列表", "parameters": [{"name": "category", "in": "query", "required": false, "description": "分类", "schema": {"type": "string"}}, {"name": "tag", "in": "query", "required": false, "description": "标签", "schema": {"type": "string"}}, {"name": "sort", "in": "query", "required": false, "description": "排序方式", "schema": {"type": "string", "enum": ["hot", "latest", "rating"], "default": "hot"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 50, "default": 20}}], "responses": {"200": {"description": "成功获取短剧列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DramaListResponse"}}}}}}}, "/dramas/{dramaId}/cuts": {"get": {"tags": ["media"], "summary": "获取短剧精彩片段", "description": "获取指定短剧的精彩片段列表", "parameters": [{"name": "dramaId", "in": "path", "required": true, "description": "短剧ID", "schema": {"type": "string"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}], "responses": {"200": {"description": "成功获取精彩片段", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CutListResponse"}}}}}}}, "/dramas/{dramaId}/news": {"get": {"tags": ["drama"], "summary": "获取短剧相关新闻", "description": "获取指定短剧的相关新闻资讯", "parameters": [{"name": "dramaId", "in": "path", "required": true, "description": "短剧ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取新闻列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewsListResponse"}}}}}}}, "/actors/{actorId}": {"get": {"tags": ["actor"], "summary": "获取演员详情", "description": "获取指定演员的详细信息，包括参演短剧、精彩片段和评论", "parameters": [{"name": "actorId", "in": "path", "required": true, "description": "演员ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取演员详情", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActorDetail"}}}}, "404": {"description": "演员不存在"}}}}, "/actors/{actorId}/dramas": {"get": {"tags": ["actor"], "summary": "获取演员作品", "description": "获取指定演员的参演作品列表", "parameters": [{"name": "actorId", "in": "path", "required": true, "description": "演员ID", "schema": {"type": "string"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}], "responses": {"200": {"description": "成功获取演员作品", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DramaListResponse"}}}}}}}, "/actors/{actorId}/cuts": {"get": {"tags": ["media"], "summary": "获取演员精彩片段", "description": "获取指定演员的精彩片段", "parameters": [{"name": "actorId", "in": "path", "required": true, "description": "演员ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取演员精彩片段", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CutListResponse"}}}}}}}, "/actors/{actorId}/follow": {"post": {"tags": ["user"], "summary": "关注演员", "description": "关注指定演员", "parameters": [{"name": "actorId", "in": "path", "required": true, "description": "演员ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "关注成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}}}, "delete": {"tags": ["user"], "summary": "取消关注演员", "description": "取消关注指定演员", "parameters": [{"name": "actorId", "in": "path", "required": true, "description": "演员ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "取消关注成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}}}}, "/comments": {"get": {"tags": ["comment"], "summary": "获取评论列表", "description": "获取指定内容的评论列表", "parameters": [{"name": "targetType", "in": "query", "required": true, "description": "评论目标类型", "schema": {"type": "string", "enum": ["drama", "actor"]}}, {"name": "targetId", "in": "query", "required": true, "description": "评论目标ID", "schema": {"type": "string"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 50, "default": 10}}], "responses": {"200": {"description": "成功获取评论列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommentListResponse"}}}}}}, "post": {"tags": ["comment"], "summary": "发布评论", "description": "发布新评论", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommentCreateRequest"}}}}, "responses": {"201": {"description": "评论发布成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommentResponse"}}}}}}}, "/ranking/tags": {"get": {"tags": ["ranking"], "summary": "获取标签排行榜", "description": "获取标签排行榜数据", "parameters": [{"name": "type", "in": "query", "required": false, "description": "榜单类型", "schema": {"type": "string", "enum": ["hot", "weird"], "default": "hot"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}], "responses": {"200": {"description": "成功获取标签排行榜", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagRankingResponse"}}}}}}}, "/ranking/actors": {"get": {"tags": ["ranking"], "summary": "获取演员排行榜", "description": "获取演员排行榜数据", "parameters": [{"name": "type", "in": "query", "required": false, "description": "榜单类型", "schema": {"type": "string", "enum": ["total", "male", "female"], "default": "total"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}], "responses": {"200": {"description": "成功获取演员排行榜", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActorRankingResponse"}}}}}}}, "/tags": {"get": {"tags": ["tag"], "summary": "获取热门标签", "description": "获取当前热门标签列表", "responses": {"200": {"description": "成功获取热门标签", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagListResponse"}}}}}}}, "/tags/{tagName}": {"get": {"tags": ["tag"], "summary": "获取标签详情", "description": "获取指定标签的详细信息", "parameters": [{"name": "tagName", "in": "path", "required": true, "description": "标签名称", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取标签详情", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagDetail"}}}}}}}, "/tags/{tagName}/dramas": {"get": {"tags": ["tag"], "summary": "获取标签下的短剧", "description": "获取指定标签下的短剧列表", "parameters": [{"name": "tagName", "in": "path", "required": true, "description": "标签名称", "schema": {"type": "string"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}], "responses": {"200": {"description": "成功获取标签下的短剧", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DramaListResponse"}}}}}}}, "/user/follows": {"get": {"tags": ["user"], "summary": "获取用户关注列表", "description": "获取当前用户关注的演员列表", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}], "responses": {"200": {"description": "成功获取关注列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActorListResponse"}}}}}}}, "/payment/unlock": {"post": {"tags": ["payment"], "summary": "解锁付费内容", "description": "购买并解锁付费内容", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentUnlockRequest"}}}}, "responses": {"200": {"description": "解锁成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentResponse"}}}}}}}, "/payment/status": {"get": {"tags": ["payment"], "summary": "查询付费状态", "description": "查询用户对特定内容的付费状态", "parameters": [{"name": "contentType", "in": "query", "required": true, "description": "内容类型", "schema": {"type": "string", "enum": ["drama", "cut"]}}, {"name": "contentId", "in": "query", "required": true, "description": "内容ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取付费状态", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentStatusResponse"}}}}}}}}, "components": {"schemas": {"HomeRecommendationResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "success"}, "data": {"type": "object", "properties": {"todayFortune": {"type": "object", "properties": {"date": {"type": "string", "example": "07/09"}, "good": {"type": "string", "example": "看甜甜的恋爱"}, "bad": {"type": "string", "example": "沉溺于痛苦"}}}, "recommendations": {"type": "array", "items": {"$ref": "#/components/schemas/RecommendationRow"}}}}}}, "RecommendationRow": {"type": "object", "properties": {"drama": {"$ref": "#/components/schemas/DuanjuResponse"}, "actor": {"$ref": "#/components/schemas/DuanjuActorResponse"}}}, "SearchResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "success"}, "data": {"type": "object", "properties": {"actors": {"type": "array", "items": {"$ref": "#/components/schemas/Actor"}}, "dramas": {"type": "array", "items": {"$ref": "#/components/schemas/Drama"}}, "cuts": {"type": "array", "items": {"$ref": "#/components/schemas/Cut"}}, "news": {"type": "array", "items": {"$ref": "#/components/schemas/News"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}, "DramaDetail": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "success"}, "data": {"allOf": [{"$ref": "#/components/schemas/Drama"}, {"type": "object", "properties": {"description": {"type": "string", "example": "这里是简介"}, "recommendation": {"type": "string", "example": "这部短剧以其紧凑的剧情..."}, "actors": {"type": "array", "items": {"$ref": "#/components/schemas/Actor"}}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["#霸道总裁", "#都市爱情", "#超甜"]}}}]}}}, "DramaListResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "success"}, "data": {"type": "object", "properties": {"dramas": {"type": "array", "items": {"$ref": "#/components/schemas/Drama"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}, "Drama": {"type": "object", "properties": {"id": {"type": "string", "example": "drama_001"}, "title": {"type": "string", "example": "霸道总裁爱上我"}, "cover": {"type": "string", "example": "https://example.com/cover.jpg"}, "producer": {"type": "string", "example": "我爱看短剧集团"}, "episodes": {"type": "integer", "example": 100}, "heat": {"type": "string", "example": "100万人正在看"}, "playCount": {"type": "string", "example": "1.2亿次播放"}}}, "Actor": {"type": "object", "properties": {"id": {"type": "string", "example": "actor_001"}, "name": {"type": "string", "example": "张三"}, "avatar": {"type": "string", "example": "https://example.com/avatar.jpg"}, "gender": {"type": "string", "enum": ["男", "女"], "example": "男"}, "heat": {"type": "string", "example": "98万热度"}, "representative": {"type": "string", "example": "《霸道总裁爱上我》、《重生之我是豪门千金》"}, "isFollowed": {"type": "boolean", "example": false}}}, "ActorDetail": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "success"}, "data": {"$ref": "#/components/schemas/DuanjuActorDetailResponse"}}}, "ActorListResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "success"}, "data": {"type": "object", "properties": {"actors": {"type": "array", "items": {"$ref": "#/components/schemas/Actor"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}, "Cut": {"type": "object", "properties": {"id": {"type": "string", "example": "cut_001"}, "title": {"type": "string", "example": "甜蜜告白"}, "cover": {"type": "string", "example": "https://example.com/cut_cover.jpg"}, "duration": {"type": "string", "example": "02:30"}, "playCount": {"type": "string", "example": "1.2万次播放"}, "needPay": {"type": "boolean", "example": false}, "videoUrl": {"type": "string", "example": "https://example.com/video.mp4"}}}, "CutListResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "success"}, "data": {"type": "object", "properties": {"cuts": {"type": "array", "items": {"$ref": "#/components/schemas/Cut"}}, "hasPaidContent": {"type": "boolean", "example": true}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}, "News": {"type": "object", "properties": {"id": {"type": "string", "example": "news_001"}, "title": {"type": "string", "example": "专访张三：拍摄《霸道总裁爱上我》的心路历程"}, "description": {"type": "string", "example": "近日，我们有幸采访到了《我在精神病院学斩神》的男主角张三..."}, "source": {"type": "string", "example": "人物志"}, "publishTime": {"type": "string", "example": "2天前"}, "cover": {"type": "string", "example": "https://example.com/news_cover.jpg"}}}, "NewsListResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "success"}, "data": {"type": "object", "properties": {"news": {"type": "array", "items": {"$ref": "#/components/schemas/News"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}, "Comment": {"type": "object", "properties": {"id": {"type": "string", "example": "comment_001"}, "author": {"type": "string", "example": "用户昵称"}, "avatar": {"type": "string", "example": "https://example.com/user_avatar.jpg"}, "content": {"type": "string", "example": "这部剧真的太好看了！"}, "time": {"type": "string", "example": "2小时前"}, "createdAt": {"type": "string", "format": "date-time", "example": "2024-07-09T10:30:00Z"}}}, "CommentListResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "success"}, "data": {"type": "object", "properties": {"comments": {"type": "array", "items": {"$ref": "#/components/schemas/Comment"}}, "total": {"type": "integer", "example": 2345}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}, "CommentCreateRequest": {"type": "object", "required": ["targetType", "targetId", "content"], "properties": {"targetType": {"type": "string", "enum": ["drama", "actor"], "example": "drama"}, "targetId": {"type": "string", "example": "drama_001"}, "content": {"type": "string", "example": "这部剧真的太好看了！"}}}, "CommentResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 201}, "message": {"type": "string", "example": "评论发布成功"}, "data": {"$ref": "#/components/schemas/Comment"}}}, "DuanjuActorCutResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "title": {"type": "string"}, "desc": {"type": "string"}, "playCount": {"type": "integer", "format": "int64"}, "duration": {"type": "integer", "format": "int64"}}}, "Tag": {"type": "object", "properties": {"id": {"type": "string", "example": "tag_001"}, "name": {"type": "string", "example": "霸道总裁"}, "heat": {"type": "string", "example": "2.1亿热度"}, "description": {"type": "string", "example": "精彩爽文短剧，让你一次看个够"}}}, "TagDetail": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "success"}, "data": {"$ref": "#/components/schemas/Tag"}}}, "TagListResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "success"}, "data": {"type": "object", "properties": {"tags": {"type": "array", "items": {"$ref": "#/components/schemas/Tag"}}}}}}, "TagSection": {"type": "object", "properties": {"tag": {"$ref": "#/components/schemas/Tag"}, "dramas": {"type": "array", "items": {"$ref": "#/components/schemas/Drama"}}}}, "TagRankingResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "success"}, "data": {"type": "object", "properties": {"sections": {"type": "array", "items": {"$ref": "#/components/schemas/TagSection"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}, "ActorRankingResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "success"}, "data": {"type": "object", "properties": {"actors": {"type": "array", "items": {"$ref": "#/components/schemas/Actor"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}, "PaymentUnlockRequest": {"type": "object", "required": ["contentType", "contentId"], "properties": {"contentType": {"type": "string", "enum": ["drama", "cut"], "example": "drama"}, "contentId": {"type": "string", "example": "drama_001"}, "paymentMethod": {"type": "string", "enum": ["wechat", "alipay", "apple_pay"], "example": "wechat"}}}, "PaymentResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "解锁成功"}, "data": {"type": "object", "properties": {"orderId": {"type": "string", "example": "order_123456"}, "unlocked": {"type": "boolean", "example": true}, "unlockedAt": {"type": "string", "format": "date-time", "example": "2024-07-09T10:30:00Z"}}}}}, "PaymentStatusResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "success"}, "data": {"type": "object", "properties": {"isPaid": {"type": "boolean", "example": true}, "unlockedAt": {"type": "string", "format": "date-time", "example": "2024-07-09T10:30:00Z"}, "orderId": {"type": "string", "example": "order_123456"}}}}}, "Pagination": {"type": "object", "properties": {"page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 20}, "total": {"type": "integer", "example": 100}, "totalPages": {"type": "integer", "example": 5}, "hasNext": {"type": "boolean", "example": true}, "hasPrev": {"type": "boolean", "example": false}}}, "SuccessResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "操作成功"}, "data": {"type": "object", "nullable": true}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 400}, "message": {"type": "string", "example": "请求参数错误"}, "error": {"type": "string", "example": "INVALID_PARAMETER"}}}, "DuanjuResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "bookName": {"type": "string"}, "bookId": {"type": "string"}, "serialCount": {"type": "number"}, "readCount": {"type": "integer", "format": "int64"}, "abstract": {"type": "string"}, "platform": {"type": "string"}, "recommendation": {"type": "string"}, "heatCount": {"type": "integer", "format": "int64"}, "actors": {"type": "array", "items": {"$ref": "#/components/schemas/DuanjuActorResponse"}}, "categories": {"type": "array", "items": {"$ref": "#/components/schemas/DuanjuCategoryResponse"}}, "authors": {"type": "array", "items": {"$ref": "#/components/schemas/DuanjuAuthorResponse"}}}}, "DuanjuActorResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "avatarUrl": {"type": "string"}, "gender": {"type": "string"}, "platform": {"type": "string"}, "actInCounts": {"type": "integer", "format": "int64"}, "desc": {"type": "string"}, "profile": {"type": "string"}, "isFollowed": {"type": "boolean"}, "socialLinks": {"$ref": "#/components/schemas/DuanjuActorSocialLinks"}}}, "DuanjuActorSocialLinks": {"type": "object", "properties": {"xhsId": {"type": "string"}, "weiboId": {"type": "string"}, "douyinId": {"type": "string"}}}, "DuanjuActorDetailResponse": {"type": "object", "properties": {"actor": {"$ref": "#/components/schemas/DuanjuActorResponse"}, "dramas": {"type": "array", "items": {"$ref": "#/components/schemas/DuanjuResponse"}}, "cuts": {"type": "array", "items": {"$ref": "#/components/schemas/DuanjuActorCutResponse"}}, "comments": {"type": "array", "items": {"$ref": "#/components/schemas/CommentResponse"}}}}, "DuanjuCategoryResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "platform": {"type": "string"}, "heatCount": {"type": "integer", "format": "int64"}, "desc": {"type": "string"}}}, "DuanjuAuthorResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "platform": {"type": "string"}, "desc": {"type": "string"}}}}, "responses": {"BadRequest": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "Unauthorized": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "NotFound": {"description": "资源不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "InternalServerError": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}, "ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-API-Key"}}}, "security": [{"BearerAuth": []}]}